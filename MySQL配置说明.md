# MySQL数据库配置说明

## 1. 数据库准备

### 安装MySQL
确保您的系统已安装MySQL 8.0或更高版本。

### 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE student_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'student_user'@'localhost' IDENTIFIED BY 'student_password';
GRANT ALL PRIVILEGES ON student_db.* TO 'student_user'@'localhost';
FLUSH PRIVILEGES;
```

## 2. 配置文件修改

### 方法一：使用MySQL配置文件
启动应用时指定使用MySQL配置：
```bash
java -jar demo6.jar --spring.profiles.active=mysql
```

### 方法二：直接修改application.properties
将以下MySQL配置复制到 `src/main/resources/application.properties`：

```properties
# MySQL数据源配置
spring.datasource.url=***********************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
```

## 3. 数据库初始化

### 自动初始化（推荐）
应用启动时会自动创建表结构，无需手动执行SQL。

### 手动初始化
如果需要手动创建表和插入数据：
```bash
mysql -u root -p student_db < src/main/resources/mysql-schema.sql
mysql -u root -p student_db < src/main/resources/mysql-data.sql
```

## 4. 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| spring.datasource.url | 数据库连接URL | localhost:3306/student_db |
| spring.datasource.username | 数据库用户名 | root |
| spring.datasource.password | 数据库密码 | 123456 |
| spring.jpa.hibernate.ddl-auto | 表结构管理策略 | update |

### ddl-auto选项说明：
- `create`: 每次启动删除并重新创建表
- `create-drop`: 启动时创建，关闭时删除
- `update`: 更新表结构（推荐）
- `validate`: 验证表结构
- `none`: 不做任何操作

## 5. 常见问题

### 连接失败
1. 检查MySQL服务是否启动
2. 确认数据库名称、用户名、密码是否正确
3. 检查防火墙设置

### 时区问题
如果遇到时区错误，在URL中添加：
```
?serverTimezone=Asia/Shanghai
```

### 字符编码问题
确保数据库和表使用utf8mb4字符集：
```sql
ALTER DATABASE student_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 6. 启动应用

### 使用H2数据库（默认）
```bash
mvn spring-boot:run
```

### 使用MySQL数据库
```bash
mvn spring-boot:run -Dspring.profiles.active=mysql
```

访问地址：http://localhost:8080
