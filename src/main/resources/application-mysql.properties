# MySQL数据库配置
# 应用名称
spring.application.name=demo6

# 服务器配置
server.port=8080

# JSP配置
spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp

# MySQL数据源配置
spring.datasource.url=***********************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# 初始化数据库（首次运行时使用）
spring.sql.init.mode=never
# 如果需要初始化数据，改为：
# spring.sql.init.mode=always
# spring.sql.init.schema-locations=classpath:mysql-schema.sql
# spring.sql.init.data-locations=classpath:mysql-data.sql

# 日志配置
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
