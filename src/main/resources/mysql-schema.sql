-- MySQL数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS student_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE student_db;

-- 删除表（如果存在）
DROP TABLE IF EXISTS studenttable;

-- 创建学生表
CREATE TABLE studenttable (
    student_id VARCHAR(20) PRIMARY KEY COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL COMMENT '性别',
    age INT NOT NULL CHECK (age > 0) COMMENT '年龄',
    department VARCHAR(100) NOT NULL COMMENT '院系',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生信息表';

-- 创建索引
CREATE INDEX idx_student_name ON studenttable(name);
CREATE INDEX idx_student_department ON studenttable(department);
CREATE INDEX idx_student_age ON studenttable(age);
