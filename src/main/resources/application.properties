# 应用名称
spring.application.name=demo6

# 服务器配置
server.port=8080
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true

# JSP配置
spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp

# MySQL数据源配置
spring.datasource.url=***********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
# Spring Boot会自动检测MySQL驱动，无需手动指定driver-class-name

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# 禁用自动执行 data.sql
spring.sql.init.mode=never
# spring.jpa.defer-datasource-initialization=true
# spring.sql.init.continue-on-error=true

# 配置文件上传大小限制
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 配置静态资源路径
spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/static/

# 日志配置
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.hibernate.SQL=DEBUG
