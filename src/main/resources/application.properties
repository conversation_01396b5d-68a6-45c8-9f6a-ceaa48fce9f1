spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
# 显示 SQL 语句
spring.jpa.properties.hibernate.format_sql=true

# 禁用自动执行 data.sql
spring.sql.init.mode=never
# spring.jpa.defer-datasource-initialization=true
# spring.sql.init.continue-on-error=true

# 配置文件上传大小限制
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 配置静态资源路径
spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/static/
