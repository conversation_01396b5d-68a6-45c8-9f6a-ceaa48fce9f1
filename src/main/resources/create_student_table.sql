-- 在现有的springtest数据库中创建学生表
USE springtest;

-- 删除表（如果存在）
DROP TABLE IF EXISTS studenttable;

-- 创建学生表
CREATE TABLE studenttable (
    student_id VARCHAR(20) PRIMARY KEY COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL COMMENT '性别',
    age INT NOT NULL CHECK (age > 0) COMMENT '年龄',
    department VARCHAR(100) NOT NULL COMMENT '院系',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生信息表';

-- 创建索引
CREATE INDEX idx_student_name ON studenttable(name);
CREATE INDEX idx_student_department ON studenttable(department);
CREATE INDEX idx_student_age ON studenttable(age);

-- 插入示例数据
INSERT INTO studenttable (student_id, name, gender, age, department) VALUES
('2021001', '张三', 'MALE', 20, '计算机科学与技术'),
('2021002', '李四', 'FEMALE', 19, '软件工程'),
('2021003', '王五', 'MALE', 21, '信息管理与信息系统'),
('2021004', '赵六', 'FEMALE', 20, '电子商务'),
('2021005', '钱七', 'OTHER', 22, '数据科学与大数据技术');

-- 查询验证
SELECT * FROM studenttable;
