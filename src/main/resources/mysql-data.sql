-- MySQL示例数据插入脚本

-- 使用数据库
USE student_db;

-- 清空表数据
TRUNCATE TABLE studenttable;

-- 插入示例学生数据
INSERT INTO studenttable (student_id, name, gender, age, department) VALUES
('2021001', '张三', 'MALE', 20, '计算机科学与技术'),
('2021002', '李四', 'FEMALE', 19, '软件工程'),
('2021003', '王五', 'MALE', 21, '信息管理与信息系统'),
('2021004', '赵六', 'FEMALE', 20, '电子商务'),
('2021005', '钱七', 'OTHER', 22, '数据科学与大数据技术'),
('2021006', '孙八', 'MALE', 19, '网络工程'),
('2021007', '周九', 'FEMALE', 21, '物联网工程'),
('2021008', '吴十', 'MALE', 20, '人工智能'),
('2021009', '郑十一', 'FEMALE', 22, '网络空间安全'),
('2021010', '王十二', 'OTHER', 19, '数字媒体技术');
